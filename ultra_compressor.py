#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ضاغط PDF فائق القوة - Ultra PDF Compressor
تقليل الحجم إلى 1 MB أو أقل باستخدام تقنيات متقدمة
"""

import fitz  # PyMuPDF
import os
import sys
from PIL import Image
import io
import tempfile

def ultra_compress_pdf_to_1mb(input_file, output_file):
    """ضغط PDF إلى 1 MB أو أقل باستخدام تقنيات متقدمة"""
    
    print(f"⚡ ضاغط PDF فائق القوة")
    print(f"🎯 الهدف: تقليل {input_file} إلى 1 MB أو أقل")
    print("=" * 60)
    
    original_size = os.path.getsize(input_file)
    target_size = 1 * 1024 * 1024  # 1 MB
    
    print(f"📊 الحجم الأصلي: {format_size(original_size)}")
    print(f"🎯 الحجم المستهدف: {format_size(target_size)}")
    
    try:
        # المرحلة 1: ضغط الصور بقوة
        print(f"\n🔥 المرحلة 1: ضغط الصور بأقصى قوة...")
        temp_file1 = compress_images_aggressively(input_file)
        
        if temp_file1:
            size1 = os.path.getsize(temp_file1)
            print(f"   📉 الحجم بعد ضغط الصور: {format_size(size1)}")
            
            if size1 <= target_size:
                os.rename(temp_file1, output_file)
                print(f"✅ تم تحقيق الهدف في المرحلة 1!")
                return True
        else:
            temp_file1 = input_file
        
        # المرحلة 2: تقليل دقة الصفحات
        print(f"\n🔥 المرحلة 2: تقليل دقة الصفحات...")
        temp_file2 = reduce_page_resolution(temp_file1)
        
        if temp_file2:
            size2 = os.path.getsize(temp_file2)
            print(f"   📉 الحجم بعد تقليل الدقة: {format_size(size2)}")
            
            if size2 <= target_size:
                os.rename(temp_file2, output_file)
                if temp_file1 != input_file:
                    os.remove(temp_file1)
                print(f"✅ تم تحقيق الهدف في المرحلة 2!")
                return True
        else:
            temp_file2 = temp_file1
        
        # المرحلة 3: إزالة البيانات الزائدة
        print(f"\n🔥 المرحلة 3: إزالة البيانات الزائدة...")
        temp_file3 = remove_unnecessary_data(temp_file2)
        
        if temp_file3:
            size3 = os.path.getsize(temp_file3)
            print(f"   📉 الحجم بعد التنظيف: {format_size(size3)}")
            
            if size3 <= target_size:
                os.rename(temp_file3, output_file)
                if temp_file2 != input_file:
                    os.remove(temp_file2)
                if temp_file1 != input_file:
                    os.remove(temp_file1)
                print(f"✅ تم تحقيق الهدف في المرحلة 3!")
                return True
        else:
            temp_file3 = temp_file2
        
        # المرحلة 4: ضغط نهائي متطرف
        print(f"\n🔥 المرحلة 4: ضغط نهائي متطرف...")
        success = extreme_final_compression(temp_file3, output_file, target_size)
        
        # تنظيف الملفات المؤقتة
        for temp_file in [temp_file1, temp_file2, temp_file3]:
            if temp_file != input_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
        
        if success:
            final_size = os.path.getsize(output_file)
            compression_ratio = (1 - final_size / original_size) * 100
            
            print(f"\n🎉 تم الضغط بنجاح!")
            print(f"📉 الحجم النهائي: {format_size(final_size)}")
            print(f"💾 نسبة الضغط: {compression_ratio:.1f}%")
            
            if final_size <= target_size:
                print(f"✅ تم تحقيق الهدف! الحجم أقل من 1 MB")
            else:
                print(f"⚠️ الحجم أكبر من المستهدف بـ {format_size(final_size - target_size)}")
            
            return True
        else:
            print(f"❌ فشل في الضغط النهائي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الضغط: {e}")
        return False

def compress_images_aggressively(input_file):
    """ضغط الصور بأقصى قوة"""
    try:
        doc = fitz.open(input_file)
        temp_file = input_file.replace('.pdf', '_temp_images.pdf')
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # الحصول على جميع الصور في الصفحة
            image_list = page.get_images(full=True)
            
            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    
                    # استخراج الصورة
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    image_ext = base_image["ext"]
                    
                    # ضغط الصورة بقوة
                    compressed_bytes = super_compress_image_bytes(image_bytes)
                    
                    if compressed_bytes and len(compressed_bytes) < len(image_bytes):
                        # إنشاء صورة جديدة مضغوطة
                        new_image = fitz.open("jpg", compressed_bytes)
                        new_pix = new_image[0].get_pixmap()
                        
                        # استبدال الصورة في المستند
                        doc._updateObject(xref, new_pix.tobytes("jpg"))
                        new_image.close()
                        
                except Exception as e:
                    print(f"   ⚠️ خطأ في ضغط صورة: {e}")
                    continue
        
        # حفظ المستند
        doc.save(temp_file, garbage=4, deflate=True, clean=True)
        doc.close()
        
        return temp_file if os.path.exists(temp_file) else None
        
    except Exception as e:
        print(f"خطأ في ضغط الصور: {e}")
        return None

def super_compress_image_bytes(image_bytes):
    """ضغط بايتات الصورة بأقصى قوة"""
    try:
        # فتح الصورة
        image = Image.open(io.BytesIO(image_bytes))
        
        # تحويل إلى RGB
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # تقليل الحجم بقوة (أقصى عرض 400 بكسل)
        max_size = 400
        if image.width > max_size or image.height > max_size:
            ratio = min(max_size / image.width, max_size / image.height)
            new_width = int(image.width * ratio)
            new_height = int(image.height * ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # ضغط بجودة منخفضة جداً
        output = io.BytesIO()
        image.save(
            output,
            format='JPEG',
            quality=20,  # جودة منخفضة جداً
            optimize=True,
            progressive=True
        )
        
        return output.getvalue()
        
    except Exception as e:
        print(f"خطأ في ضغط بايتات الصورة: {e}")
        return None

def reduce_page_resolution(input_file):
    """تقليل دقة الصفحات"""
    try:
        doc = fitz.open(input_file)
        temp_file = input_file.replace('.pdf', '_temp_resolution.pdf')
        
        # إنشاء مستند جديد بدقة منخفضة
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # تحويل الصفحة إلى صورة بدقة منخفضة
            mat = fitz.Matrix(0.5, 0.5)  # تقليل الدقة إلى النصف
            pix = page.get_pixmap(matrix=mat, alpha=False)
            
            # إنشاء صفحة جديدة
            img_doc = fitz.open("png", pix.tobytes("png"))
            new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
            new_page.insert_image(page.rect, stream=pix.tobytes("jpg"))
            img_doc.close()
        
        # حفظ المستند الجديد
        new_doc.save(temp_file, garbage=4, deflate=True, clean=True)
        new_doc.close()
        doc.close()
        
        return temp_file if os.path.exists(temp_file) else None
        
    except Exception as e:
        print(f"خطأ في تقليل الدقة: {e}")
        return None

def remove_unnecessary_data(input_file):
    """إزالة البيانات غير الضرورية"""
    try:
        doc = fitz.open(input_file)
        temp_file = input_file.replace('.pdf', '_temp_clean.pdf')
        
        # إزالة البيانات الزائدة
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # إزالة التعليقات التوضيحية
            annots = page.annots()
            for annot in annots:
                page.delete_annot(annot)
        
        # حفظ مع تنظيف شامل
        doc.save(
            temp_file,
            garbage=4,      # إزالة البيانات غير المستخدمة
            deflate=True,   # ضغط إضافي
            clean=True,     # تنظيف شامل
            ascii=False,    # تحسين الحجم
            expand=0,       # عدم توسيع المحتوى
            linear=False,   # تحسين للحجم
            pretty=False,   # عدم تنسيق جميل
            encryption=fitz.PDF_ENCRYPT_NONE  # بدون تشفير
        )
        
        doc.close()
        
        return temp_file if os.path.exists(temp_file) else None
        
    except Exception as e:
        print(f"خطأ في التنظيف: {e}")
        return None

def extreme_final_compression(input_file, output_file, target_size):
    """ضغط نهائي متطرف"""
    try:
        doc = fitz.open(input_file)
        
        # تحويل كل صفحة إلى صورة مضغوطة بقوة
        new_doc = fitz.open()
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # تحويل إلى صورة بدقة منخفضة جداً
            mat = fitz.Matrix(0.3, 0.3)  # دقة منخفضة جداً
            pix = page.get_pixmap(matrix=mat, alpha=False)
            
            # ضغط الصورة
            img_bytes = pix.tobytes("jpg")
            compressed_img = super_compress_image_bytes(img_bytes)
            
            if compressed_img:
                # إنشاء صفحة جديدة
                new_page = new_doc.new_page(width=page.rect.width, height=page.rect.height)
                new_page.insert_image(page.rect, stream=compressed_img)
        
        # حفظ مع أقصى ضغط
        new_doc.save(
            output_file,
            garbage=4,
            deflate=True,
            clean=True,
            ascii=False,
            expand=0,
            linear=False,
            pretty=False
        )
        
        new_doc.close()
        doc.close()
        
        return os.path.exists(output_file)
        
    except Exception as e:
        print(f"خطأ في الضغط النهائي: {e}")
        return False

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def main():
    """الدالة الرئيسية"""
    input_file = "file.pdf"
    output_file = "file_ultra_compressed_1MB.pdf"
    
    print("⚡ ضاغط PDF فائق القوة")
    print("🎯 تقليل الحجم إلى 1 MB أو أقل")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"❌ الملف {input_file} غير موجود!")
        return
    
    # بدء الضغط الفائق
    success = ultra_compress_pdf_to_1mb(input_file, output_file)
    
    if success:
        print(f"\n🎊 تم إنشاء الملف المضغوط: {output_file}")
        
        # مقارنة الأحجام
        original_size = os.path.getsize(input_file)
        final_size = os.path.getsize(output_file)
        
        print(f"\n📊 مقارنة الأحجام:")
        print(f"   📄 الملف الأصلي: {format_size(original_size)}")
        print(f"   📉 الملف المضغوط: {format_size(final_size)}")
        print(f"   💾 التوفير: {format_size(original_size - final_size)}")
        print(f"   📈 نسبة الضغط: {((original_size - final_size) / original_size * 100):.1f}%")
        
    else:
        print(f"\n❌ فشل في الضغط")
    
    input("\n⏎ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
