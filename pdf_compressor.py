#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Compressor with GUI
ضاغط ملفات PDF مع واجهة رسومية

Author: AI Assistant
Description: A powerful PDF compression tool with drag-and-drop support
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import tkinterdnd2 as tkdnd
import fitz  # PyMuPDF
import os
import threading
from pathlib import Path
import sys
from config import GUI_CONFIG, QUALITY_SETTINGS, COMPRESSION_CONFIG, get_message, get_quality_config

class PDFCompressor:
    def __init__(self, root):
        self.root = root
        self.setup_ui()
        self.input_file = None
        self.output_file = None
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        self.root.title("ضاغط ملفات PDF - PDF Compressor")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f0f0')
        
        # إعداد الأيقونة
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # العنوان
        title_label = ttk.Label(main_frame, text="ضاغط ملفات PDF", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # منطقة السحب والإفلات
        self.drop_frame = tk.Frame(main_frame, bg='#e8e8e8',
                                  relief='ridge', bd=2, height=100)
        self.drop_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), 
                            pady=(0, 20))
        self.drop_frame.grid_propagate(False)
        
        drop_label = tk.Label(self.drop_frame, 
                             text="اسحب ملف PDF هنا أو انقر لاختيار ملف\nDrag PDF file here or click to select",
                             bg='#e8e8e8', font=('Arial', 10))
        drop_label.place(relx=0.5, rely=0.5, anchor='center')
        
        # ربط أحداث السحب والإفلات
        self.drop_frame.drop_target_register(tkdnd.DND_FILES)
        self.drop_frame.dnd_bind('<<Drop>>', self.on_drop)
        self.drop_frame.bind('<Button-1>', self.select_file)
        drop_label.bind('<Button-1>', self.select_file)
        
        # معلومات الملف المحدد
        self.file_info_frame = ttk.LabelFrame(main_frame, text="معلومات الملف", padding="10")
        self.file_info_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), 
                                 pady=(0, 20))
        
        self.file_name_label = ttk.Label(self.file_info_frame, text="لم يتم اختيار ملف")
        self.file_name_label.grid(row=0, column=0, sticky=tk.W)
        
        self.file_size_label = ttk.Label(self.file_info_frame, text="")
        self.file_size_label.grid(row=1, column=0, sticky=tk.W)
        
        # إعدادات الضغط
        settings_frame = ttk.LabelFrame(main_frame, text="إعدادات الضغط", padding="10")
        settings_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), 
                           pady=(0, 20))
        
        ttk.Label(settings_frame, text="مستوى الجودة:").grid(row=0, column=0, sticky=tk.W)
        
        self.quality_var = tk.StringVar(value="متوسط")
        quality_combo = ttk.Combobox(settings_frame, textvariable=self.quality_var,
                                   values=["مرتفع", "متوسط", "منخفض", "ضغط فائق (1MB)"], state="readonly")
        quality_combo.grid(row=0, column=1, padx=(10, 0), sticky=tk.W)
        
        # شريط التقدم
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var,
                                          maximum=100, length=400)
        self.progress_bar.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E),
                              pady=(0, 10))
        
        # تسمية حالة التقدم
        self.status_label = ttk.Label(main_frame, text="جاهز للاستخدام")
        self.status_label.grid(row=5, column=0, columnspan=2, pady=(0, 20))
        
        # الأزرار
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=2)
        
        self.compress_button = ttk.Button(button_frame, text="ضغط الملف", 
                                        command=self.compress_pdf, state='disabled')
        self.compress_button.grid(row=0, column=0, padx=(0, 10))
        
        self.save_button = ttk.Button(button_frame, text="حفظ باسم", 
                                    command=self.save_as, state='disabled')
        self.save_button.grid(row=0, column=1, padx=(0, 10))
        
        clear_button = ttk.Button(button_frame, text="مسح", command=self.clear_all)
        clear_button.grid(row=0, column=2)
        
        # تكوين الشبكة
        main_frame.columnconfigure(0, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        
    def on_drop(self, event):
        """معالج حدث السحب والإفلات"""
        files = self.root.tk.splitlist(event.data)
        if files:
            file_path = files[0]
            if file_path.lower().endswith('.pdf'):
                self.load_file(file_path)
            else:
                messagebox.showerror("خطأ", "يرجى اختيار ملف PDF فقط")
    
    def select_file(self, event=None):
        """اختيار ملف من خلال مربع الحوار"""
        file_path = filedialog.askopenfilename(
            title="اختر ملف PDF",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if file_path:
            self.load_file(file_path)
    
    def load_file(self, file_path):
        """تحميل ملف PDF"""
        try:
            self.input_file = file_path
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            
            self.file_name_label.config(text=f"الملف: {file_name}")
            self.file_size_label.config(text=f"الحجم: {self.format_size(file_size)}")
            
            self.compress_button.config(state='normal')
            self.status_label.config(text="ملف محمل وجاهز للضغط")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الملف: {str(e)}")
    
    def format_size(self, size_bytes):
        """تنسيق حجم الملف"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.2f} {size_names[i]}"
    
    def get_quality_settings(self):
        """الحصول على إعدادات الجودة"""
        quality = self.quality_var.get()
        if quality == "مرتفع":
            return {"quality": 95, "dpi": 150, "ultra": False}
        elif quality == "متوسط":
            return {"quality": 75, "dpi": 100, "ultra": False}
        elif quality == "منخفض":
            return {"quality": 50, "dpi": 72, "ultra": False}
        else:  # ضغط فائق (1MB)
            return {"quality": 20, "dpi": 50, "ultra": True}
    
    def compress_pdf(self):
        """ضغط ملف PDF"""
        if not self.input_file:
            messagebox.showerror("خطأ", "يرجى اختيار ملف PDF أولاً")
            return
        
        # تشغيل الضغط في خيط منفصل
        thread = threading.Thread(target=self._compress_thread)
        thread.daemon = True
        thread.start()
    
    def _compress_thread(self):
        """خيط ضغط PDF"""
        try:
            self.root.after(0, lambda: self.status_label.config(text="جاري الضغط..."))
            self.root.after(0, lambda: self.progress_var.set(10))

            quality_settings = self.get_quality_settings()

            # التحقق من نوع الضغط
            if quality_settings.get("ultra", False):
                # استخدام الضغط الفائق
                self.root.after(0, lambda: self.status_label.config(text="ضغط فائق جاري..."))
                success = self._ultra_compress()
                if success:
                    self.root.after(0, lambda: self.progress_var.set(100))
                    return

            # الضغط العادي
            # فتح المستند
            doc = fitz.open(self.input_file)

            # إنشاء ملف مؤقت للنتيجة
            temp_output = self.input_file.replace('.pdf', '_compressed_temp.pdf')

            self.root.after(0, lambda: self.progress_var.set(30))
            
            # ضغط الصور في كل صفحة
            total_pages = len(doc)
            for page_num in range(total_pages):
                page = doc[page_num]
                
                # الحصول على قائمة الصور
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    
                    # ضغط الصورة
                    compressed_image = self._compress_image(image_bytes, quality_settings)
                    
                    # استبدال الصورة
                    doc._updateObject(xref, compressed_image)
                
                # تحديث شريط التقدم
                progress = 30 + (page_num + 1) / total_pages * 50
                self.root.after(0, lambda p=progress: self.progress_var.set(p))
            
            self.root.after(0, lambda: self.progress_var.set(80))
            
            # حفظ المستند المضغوط
            doc.save(temp_output, garbage=4, deflate=True)
            doc.close()
            
            self.root.after(0, lambda: self.progress_var.set(90))
            
            # نقل الملف المؤقت
            final_output = self.input_file.replace('.pdf', '_compressed.pdf')
            if os.path.exists(final_output):
                os.remove(final_output)
            os.rename(temp_output, final_output)
            
            self.output_file = final_output
            
            # حساب نسبة الضغط
            original_size = os.path.getsize(self.input_file)
            compressed_size = os.path.getsize(final_output)
            compression_ratio = (1 - compressed_size / original_size) * 100
            
            self.root.after(0, lambda: self.progress_var.set(100))
            self.root.after(0, lambda: self.status_label.config(
                text=f"تم الضغط بنجاح! توفير: {compression_ratio:.1f}% - الحجم الجديد: {self.format_size(compressed_size)}"))
            self.root.after(0, lambda: self.save_button.config(state='normal'))
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("خطأ", f"فشل في ضغط الملف: {str(e)}"))
            self.root.after(0, lambda: self.status_label.config(text="فشل في الضغط"))
        finally:
            self.root.after(0, lambda: self.progress_var.set(0))
    
    def _compress_image(self, image_bytes, quality_settings):
        """ضغط صورة فردية باستخدام Pillow"""
        try:
            from PIL import Image
            import io

            # تحويل البايتات إلى صورة
            image = Image.open(io.BytesIO(image_bytes))

            # تحويل إلى RGB إذا لزم الأمر
            if image.mode in ('RGBA', 'LA', 'P'):
                image = image.convert('RGB')

            # تقليل الدقة إذا كانت عالية جداً
            max_dimension = 1200 if quality_settings["quality"] > 70 else 800
            if max(image.size) > max_dimension:
                image.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)

            # ضغط الصورة
            output = io.BytesIO()
            image.save(output, format='JPEG',
                      quality=quality_settings["quality"],
                      optimize=True)

            return output.getvalue()

        except Exception as e:
            print(f"خطأ في ضغط الصورة: {e}")
            return image_bytes

    def _ultra_compress(self):
        """ضغط فائق إلى 1 MB أو أقل"""
        try:
            from ultra_compressor import ultra_compress_pdf_to_1mb

            # إنشاء اسم الملف المضغوط
            output_file = self.input_file.replace('.pdf', '_ultra_compressed.pdf')

            # تشغيل الضغط الفائق
            success = ultra_compress_pdf_to_1mb(self.input_file, output_file)

            if success and os.path.exists(output_file):
                self.output_file = output_file

                # حساب النتائج
                original_size = os.path.getsize(self.input_file)
                compressed_size = os.path.getsize(output_file)
                compression_ratio = (1 - compressed_size / original_size) * 100

                self.root.after(0, lambda: self.status_label.config(
                    text=f"ضغط فائق مكتمل! توفير: {compression_ratio:.1f}% - الحجم: {self.format_size(compressed_size)}"))
                self.root.after(0, lambda: self.save_button.config(state='normal'))

                return True
            else:
                self.root.after(0, lambda: self.status_label.config(text="فشل في الضغط الفائق"))
                return False

        except Exception as e:
            self.root.after(0, lambda: self.status_label.config(text=f"خطأ في الضغط الفائق: {str(e)}"))
            return False

    def save_as(self):
        """حفظ الملف المضغوط باسم جديد"""
        if not self.output_file:
            messagebox.showerror("خطأ", "لا يوجد ملف مضغوط للحفظ")
            return
        
        save_path = filedialog.asksaveasfilename(
            title="حفظ الملف المضغوط",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        
        if save_path:
            try:
                import shutil
                shutil.copy2(self.output_file, save_path)
                messagebox.showinfo("نجح", f"تم حفظ الملف في:\n{save_path}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {str(e)}")
    
    def clear_all(self):
        """مسح جميع البيانات"""
        self.input_file = None
        self.output_file = None
        self.file_name_label.config(text="لم يتم اختيار ملف")
        self.file_size_label.config(text="")
        self.status_label.config(text="جاهز للاستخدام")
        self.progress_var.set(0)
        self.compress_button.config(state='disabled')
        self.save_button.config(state='disabled')

def main():
    """الدالة الرئيسية"""
    root = tkdnd.Tk()
    app = PDFCompressor(root)
    
    # التعامل مع إغلاق النافذة
    def on_closing():
        root.quit()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
