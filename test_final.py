#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل لضاغط PDF
Final comprehensive test for PDF compressor
"""

import os
import sys

def test_all_compression_methods():
    """اختبار جميع طرق الضغط"""
    
    print("🎯 اختبار نهائي شامل لضاغط PDF")
    print("🎯 Final comprehensive test for PDF compressor")
    print("=" * 60)
    
    input_file = "file.pdf"
    
    if not os.path.exists(input_file):
        print(f"❌ الملف {input_file} غير موجود!")
        return
    
    original_size = os.path.getsize(input_file)
    print(f"📄 الملف الأصلي: {input_file}")
    print(f"📊 الحجم الأصلي: {format_size(original_size)}")
    print()
    
    # قائمة الملفات المضغوطة المتوقعة
    compressed_files = [
        ("file_compressed_مرتفع.pdf", "ضغط عادي - جودة مرتفعة"),
        ("file_compressed_متوسط.pdf", "ضغط عادي - جودة متوسطة"),
        ("file_compressed_منخفض.pdf", "ضغط عادي - جودة منخفضة"),
        ("file_compressed_1MB.pdf", "ضغط متقدم"),
        ("file_ultra_compressed_1MB.pdf", "ضغط فائق - الهدف 1MB")
    ]
    
    print("📊 نتائج الضغط:")
    print("-" * 60)
    
    target_1mb = 1 * 1024 * 1024  # 1 MB بالبايت
    
    for filename, description in compressed_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            compression_ratio = (1 - size / original_size) * 100
            
            # تحديد الحالة
            if size <= target_1mb:
                status = "✅ تحقق الهدف"
            else:
                status = "⚠️ أكبر من 1MB"
            
            print(f"📁 {description}")
            print(f"   📄 الملف: {filename}")
            print(f"   📊 الحجم: {format_size(size)}")
            print(f"   💾 التوفير: {compression_ratio:.1f}%")
            print(f"   🎯 الحالة: {status}")
            print()
        else:
            print(f"❌ {description}: الملف غير موجود")
            print(f"   📄 {filename}")
            print()
    
    # العثور على أفضل ضغط
    best_file = None
    best_size = original_size
    best_description = ""
    
    for filename, description in compressed_files:
        if os.path.exists(filename):
            size = os.path.getsize(filename)
            if size < best_size:
                best_size = size
                best_file = filename
                best_description = description
    
    if best_file:
        best_compression = (1 - best_size / original_size) * 100
        print("🏆 أفضل نتيجة ضغط:")
        print(f"   📁 الطريقة: {best_description}")
        print(f"   📄 الملف: {best_file}")
        print(f"   📊 الحجم: {format_size(best_size)}")
        print(f"   💾 التوفير: {format_size(original_size - best_size)}")
        print(f"   📈 نسبة الضغط: {best_compression:.1f}%")
        
        if best_size <= target_1mb:
            print(f"   🎯 النتيجة: ✅ تم تحقيق هدف 1MB بنجاح!")
        else:
            print(f"   🎯 النتيجة: ⚠️ لم يتم تحقيق هدف 1MB")
    
    print("\n" + "=" * 60)
    print("🎊 انتهى الاختبار النهائي!")

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def test_gui_functionality():
    """اختبار وظائف الواجهة الرسومية"""
    print("\n🖥️ اختبار وظائف الواجهة الرسومية:")
    print("-" * 40)
    
    try:
        import pdf_compressor
        print("✅ الواجهة الرسومية: يمكن استيرادها")
        
        # اختبار الإعدادات
        from config import QUALITY_SETTINGS
        print(f"✅ إعدادات الجودة: {len(QUALITY_SETTINGS)} مستويات")
        
        # اختبار المكتبات
        import tkinterdnd2
        print("✅ مكتبة السحب والإفلات: متوفرة")
        
        import fitz
        print("✅ مكتبة PyMuPDF: متوفرة")
        
        from PIL import Image
        print("✅ مكتبة Pillow: متوفرة")
        
        print("🎉 جميع مكونات الواجهة تعمل بشكل صحيح!")
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الواجهة: {e}")

def show_project_summary():
    """عرض ملخص المشروع"""
    print("\n📋 ملخص المشروع:")
    print("=" * 60)
    
    # عد الملفات
    python_files = [f for f in os.listdir('.') if f.endswith('.py')]
    pdf_files = [f for f in os.listdir('.') if f.endswith('.pdf')]
    
    print(f"📁 ملفات Python: {len(python_files)}")
    for f in python_files:
        print(f"   📄 {f}")
    
    print(f"\n📁 ملفات PDF: {len(pdf_files)}")
    for f in pdf_files:
        size = os.path.getsize(f)
        print(f"   📄 {f} - {format_size(size)}")
    
    print(f"\n📊 إحصائيات:")
    print(f"   🔧 ملفات البرنامج: {len(python_files)}")
    print(f"   📄 ملفات PDF: {len(pdf_files)}")
    print(f"   💾 إجمالي حجم PDF: {format_size(sum(os.path.getsize(f) for f in pdf_files))}")

def main():
    """الدالة الرئيسية"""
    print("🔬 مختبر ضاغط PDF - الاختبار النهائي الشامل")
    print("🔬 PDF Compressor Lab - Final Comprehensive Test")
    print("=" * 60)
    
    # اختبار طرق الضغط
    test_all_compression_methods()
    
    # اختبار الواجهة الرسومية
    test_gui_functionality()
    
    # عرض ملخص المشروع
    show_project_summary()
    
    print(f"\n🎊 المشروع مكتمل وجاهز للاستخدام!")
    print(f"🚀 لتشغيل الواجهة الرسومية: python pdf_compressor.py")
    print(f"⚡ للضغط الفائق المباشر: python ultra_compressor.py")
    
    input("\n⏎ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
