# 🚀 دليل البدء السريع - Quick Start Guide

## التثبيت السريع | Quick Installation

### الطريقة الأولى: التثبيت التلقائي
```bash
python install.py
```

### الطريقة الثانية: التثبيت اليدوي
```bash
pip install -r requirements.txt
```

## 🎯 التشغيل | Running

### Windows
```bash
python pdf_compressor.py
# أو
run.bat
```

### Linux/Mac
```bash
python3 pdf_compressor.py
# أو
chmod +x run.sh
./run.sh
```

## 📋 خطوات الاستخدام | Usage Steps

1. **تشغيل البرنامج** - Run the program
2. **اختيار الملف** - Select PDF file:
   - اسحب الملف إلى المنطقة المخصصة
   - أو انقر لاختيار الملف
3. **اختيار مستوى الجودة** - Choose quality level:
   - مرتفع (95% جودة)
   - متوسط (75% جودة) 
   - منخفض (50% جودة)
4. **الضغط** - Compress: انقر "ضغط الملف"
5. **الحفظ** - Save: انقر "حفظ باسم"

## 🔧 استكشاف الأخطاء | Troubleshooting

### خطأ في tkinterdnd2
```bash
pip install --upgrade tkinterdnd2
```

### خطأ في PyMuPDF
```bash
pip install --upgrade PyMuPDF
```

### مشاكل Linux GUI
```bash
sudo apt-get install python3-tk
```

## 📊 نصائح للحصول على أفضل ضغط

- **للمستندات النصية**: استخدم الجودة المتوسطة
- **للصور عالية الدقة**: استخدم الجودة المرتفعة
- **لتوفير المساحة**: استخدم الجودة المنخفضة
- **الملفات الكبيرة**: قد تستغرق وقتاً أطول

## 🎉 مميزات إضافية

- ✅ دعم السحب والإفلات
- ✅ شريط التقدم
- ✅ عرض نسبة الضغط
- ✅ واجهة ثنائية اللغة
- ✅ معالجة الأخطاء
- ✅ حفظ مرن

---
**استمتع بضغط ملفاتك! 🎊**
