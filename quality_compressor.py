#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ضاغط PDF عالي الجودة - High Quality PDF Compressor
يحقق التوازن المثالي بين الحجم والجودة
"""

import fitz  # PyMuPDF
import os
import sys
from PIL import Image
import io

def high_quality_compress(input_file, output_file, target_size_mb=1):
    """ضغط عالي الجودة مع تحقيق هدف الحجم"""
    
    print(f"💎 ضاغط PDF عالي الجودة")
    print(f"🎯 الهدف: {target_size_mb} MB مع الحفاظ على الجودة العالية")
    print("=" * 60)
    
    original_size = os.path.getsize(input_file)
    target_size = target_size_mb * 1024 * 1024
    
    print(f"📊 الحجم الأصلي: {format_size(original_size)}")
    print(f"🎯 الحجم المستهدف: {format_size(target_size)}")
    
    try:
        # المرحلة 1: تحليل وضغط ذكي للصور
        print(f"\n🔍 المرحلة 1: تحليل وضغط ذكي للصور...")
        temp_file1 = compress_images_intelligently(input_file)
        
        if temp_file1:
            size1 = os.path.getsize(temp_file1)
            print(f"   📉 الحجم بعد ضغط الصور: {format_size(size1)}")
            
            if size1 <= target_size:
                os.rename(temp_file1, output_file)
                print(f"✅ تم تحقيق الهدف في المرحلة 1!")
                return True
        else:
            temp_file1 = input_file
        
        # المرحلة 2: تحسين هيكل PDF
        print(f"\n🔧 المرحلة 2: تحسين هيكل PDF...")
        temp_file2 = optimize_pdf_structure(temp_file1)
        
        if temp_file2:
            size2 = os.path.getsize(temp_file2)
            print(f"   📉 الحجم بعد التحسين: {format_size(size2)}")
            
            if size2 <= target_size:
                os.rename(temp_file2, output_file)
                if temp_file1 != input_file:
                    os.remove(temp_file1)
                print(f"✅ تم تحقيق الهدف في المرحلة 2!")
                return True
        else:
            temp_file2 = temp_file1
        
        # المرحلة 3: ضغط متدرج للصور
        print(f"\n📐 المرحلة 3: ضغط متدرج للصور...")
        success = gradual_image_compression(temp_file2, output_file, target_size)
        
        # تنظيف الملفات المؤقتة
        for temp_file in [temp_file1, temp_file2]:
            if temp_file != input_file and os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass
        
        if success:
            final_size = os.path.getsize(output_file)
            compression_ratio = (1 - final_size / original_size) * 100
            
            print(f"\n🎉 تم الضغط عالي الجودة بنجاح!")
            print(f"📉 الحجم النهائي: {format_size(final_size)}")
            print(f"💾 نسبة الضغط: {compression_ratio:.1f}%")
            
            if final_size <= target_size:
                print(f"✅ تم تحقيق الهدف مع الحفاظ على الجودة العالية!")
            else:
                excess = final_size - target_size
                print(f"⚠️ الحجم أكبر من المستهدف بـ {format_size(excess)}")
                print(f"💎 لكن الجودة عالية جداً")
            
            return True
        else:
            print(f"❌ فشل في الضغط")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الضغط: {e}")
        return False

def compress_images_intelligently(input_file):
    """ضغط ذكي للصور مع الحفاظ على الجودة"""
    try:
        doc = fitz.open(input_file)
        temp_file = input_file.replace('.pdf', '_temp_smart.pdf')
        
        print(f"   🔍 تحليل {len(doc)} صفحة...")
        
        images_processed = 0
        total_savings = 0
        
        for page_num in range(len(doc)):
            page = doc[page_num]
            images = page.get_images(full=True)
            
            for img_index, img in enumerate(images):
                try:
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    original_size = len(image_bytes)
                    
                    # ضغط ذكي بناءً على حجم الصورة
                    if original_size > 100000:  # أكبر من 100KB
                        quality = 75  # جودة جيدة
                        max_dim = 1200
                    elif original_size > 50000:  # أكبر من 50KB
                        quality = 80  # جودة عالية
                        max_dim = 1400
                    else:  # صور صغيرة
                        quality = 85  # جودة عالية جداً
                        max_dim = 1600
                    
                    compressed_bytes = intelligent_image_compress(
                        image_bytes, quality, max_dim
                    )
                    
                    if compressed_bytes and len(compressed_bytes) < original_size:
                        savings = original_size - len(compressed_bytes)
                        total_savings += savings
                        images_processed += 1
                        
                        # محاولة استبدال الصورة (بطريقة آمنة)
                        try:
                            # إنشاء صورة جديدة
                            new_img = fitz.open("jpg", compressed_bytes)
                            new_pix = new_img[0].get_pixmap()
                            
                            # الحصول على معلومات الصورة الأصلية
                            img_rect = page.get_image_rects(xref)[0] if page.get_image_rects(xref) else fitz.Rect(0, 0, 100, 100)
                            
                            # حذف الصورة القديمة وإدراج الجديدة
                            page.delete_image(xref)
                            page.insert_image(img_rect, pixmap=new_pix)
                            
                            new_img.close()
                        except:
                            # إذا فشل الاستبدال، نتجاهل هذه الصورة
                            pass
                    
                except Exception as e:
                    print(f"   ⚠️ تحذير في صورة: {e}")
                    continue
        
        print(f"   ✅ تم معالجة {images_processed} صورة")
        print(f"   💾 توفير إجمالي: {format_size(total_savings)}")
        
        # حفظ مع إعدادات محسنة
        doc.save(
            temp_file,
            garbage=2,      # تنظيف متوسط
            deflate=True,   # ضغط إضافي
            clean=True      # تنظيف
        )
        doc.close()
        
        return temp_file if os.path.exists(temp_file) else None
        
    except Exception as e:
        print(f"خطأ في ضغط الصور: {e}")
        return None

def intelligent_image_compress(image_bytes, quality, max_dimension):
    """ضغط ذكي للصورة مع الحفاظ على التفاصيل"""
    try:
        image = Image.open(io.BytesIO(image_bytes))
        
        # الحفاظ على معلومات الصورة الأصلية
        original_format = image.format
        original_mode = image.mode
        
        # تحويل ذكي للألوان
        if image.mode == 'RGBA':
            # إنشاء خلفية بيضاء للشفافية
            background = Image.new('RGB', image.size, (255, 255, 255))
            background.paste(image, mask=image.split()[-1])
            image = background
        elif image.mode == 'LA':
            image = image.convert('L')  # رمادي
        elif image.mode == 'P':
            image = image.convert('RGB')
        
        # تقليل الحجم بحذر شديد
        if image.width > max_dimension or image.height > max_dimension:
            # حساب النسبة للحفاظ على النسب
            ratio = min(max_dimension / image.width, max_dimension / image.height)
            
            # تقليل طفيف فقط إذا كانت الصورة كبيرة جداً
            if ratio < 0.8:  # تقليل فقط إذا كان التقليل أكثر من 20%
                new_width = int(image.width * ratio)
                new_height = int(image.height * ratio)
                
                # استخدام أفضل خوارزمية تقليل الحجم
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # ضغط بإعدادات محسنة للجودة
        output = io.BytesIO()
        
        # إعدادات ضغط متقدمة
        if image.mode == 'L':  # صور رمادية
            image.save(output, format='JPEG', quality=quality + 5, optimize=True)
        else:  # صور ملونة
            image.save(
                output,
                format='JPEG',
                quality=quality,
                optimize=True,
                progressive=True,
                subsampling=0 if quality > 80 else 1  # تحسين الجودة
            )
        
        compressed_data = output.getvalue()
        
        # التأكد من أن الضغط مفيد
        if len(compressed_data) < len(image_bytes) * 0.95:  # توفير على الأقل 5%
            return compressed_data
        else:
            return None  # لا فائدة من الضغط
        
    except Exception as e:
        print(f"خطأ في ضغط الصورة: {e}")
        return None

def optimize_pdf_structure(input_file):
    """تحسين هيكل PDF"""
    try:
        doc = fitz.open(input_file)
        temp_file = input_file.replace('.pdf', '_temp_optimized.pdf')
        
        print(f"   🔧 تحسين هيكل المستند...")
        
        # إزالة العناصر غير الضرورية فقط
        for page_num in range(len(doc)):
            page = doc[page_num]
            
            # إزالة التعليقات التوضيحية غير المرئية فقط
            annots_to_remove = []
            for annot in page.annots():
                if annot.type[1] in ['Highlight', 'Underline', 'StrikeOut']:
                    # الحفاظ على التعليقات المرئية
                    continue
                elif annot.type[1] in ['Link']:
                    # الحفاظ على الروابط
                    continue
                else:
                    # إزالة التعليقات غير المهمة فقط
                    if not annot.info.get('content', '').strip():
                        annots_to_remove.append(annot)
            
            for annot in annots_to_remove:
                page.delete_annot(annot)
        
        # حفظ مع تحسين متوسط
        doc.save(
            temp_file,
            garbage=1,      # تنظيف خفيف
            deflate=True,   # ضغط إضافي
            clean=True,     # تنظيف
            pretty=False    # توفير مساحة
        )
        doc.close()
        
        print(f"   ✅ تم تحسين هيكل المستند")
        
        return temp_file if os.path.exists(temp_file) else None
        
    except Exception as e:
        print(f"خطأ في تحسين الهيكل: {e}")
        return None

def gradual_image_compression(input_file, output_file, target_size):
    """ضغط متدرج للصور للوصول للحجم المستهدف"""
    try:
        current_size = os.path.getsize(input_file)
        
        if current_size <= target_size:
            # نسخ الملف كما هو
            import shutil
            shutil.copy2(input_file, output_file)
            return True
        
        print(f"   📐 تطبيق ضغط متدرج...")
        
        # حساب نسبة التقليل المطلوبة
        reduction_needed = (current_size - target_size) / current_size
        
        doc = fitz.open(input_file)
        
        # تطبيق ضغط متدرج بناءً على النسبة المطلوبة
        if reduction_needed > 0.3:  # تقليل أكثر من 30%
            quality = 65
            max_dim = 1000
        elif reduction_needed > 0.2:  # تقليل أكثر من 20%
            quality = 70
            max_dim = 1100
        elif reduction_needed > 0.1:  # تقليل أكثر من 10%
            quality = 75
            max_dim = 1200
        else:  # تقليل أقل من 10%
            quality = 80
            max_dim = 1300
        
        print(f"   🎯 جودة مستهدفة: {quality}%")
        print(f"   📏 حد أقصى للأبعاد: {max_dim}px")
        
        # تطبيق الضغط المتدرج
        for page_num in range(len(doc)):
            page = doc[page_num]
            images = page.get_images(full=True)
            
            for img_index, img in enumerate(images):
                try:
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    
                    compressed_bytes = intelligent_image_compress(
                        image_bytes, quality, max_dim
                    )
                    
                    if compressed_bytes:
                        # محاولة استبدال الصورة
                        try:
                            new_img = fitz.open("jpg", compressed_bytes)
                            new_pix = new_img[0].get_pixmap()
                            
                            img_rect = page.get_image_rects(xref)[0] if page.get_image_rects(xref) else fitz.Rect(0, 0, 100, 100)
                            
                            page.delete_image(xref)
                            page.insert_image(img_rect, pixmap=new_pix)
                            
                            new_img.close()
                        except:
                            pass
                    
                except Exception as e:
                    continue
        
        # حفظ النتيجة النهائية
        doc.save(
            output_file,
            garbage=2,
            deflate=True,
            clean=True
        )
        doc.close()
        
        final_size = os.path.getsize(output_file)
        print(f"   📉 الحجم النهائي: {format_size(final_size)}")
        
        return True
        
    except Exception as e:
        print(f"خطأ في الضغط المتدرج: {e}")
        return False

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def main():
    """الدالة الرئيسية"""
    input_file = "file.pdf"
    output_file = "file_high_quality_compressed.pdf"
    
    print("💎 ضاغط PDF عالي الجودة")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"❌ الملف {input_file} غير موجود!")
        return
    
    # بدء الضغط عالي الجودة
    success = high_quality_compress(input_file, output_file, target_size_mb=1)
    
    if success:
        print(f"\n🎊 تم إنشاء الملف المضغوط عالي الجودة: {output_file}")
        
        # مقارنة النتائج
        original_size = os.path.getsize(input_file)
        final_size = os.path.getsize(output_file)
        
        print(f"\n📊 مقارنة النتائج:")
        print(f"   📄 الملف الأصلي: {format_size(original_size)}")
        print(f"   💎 الملف عالي الجودة: {format_size(final_size)}")
        print(f"   💾 التوفير: {format_size(original_size - final_size)}")
        print(f"   📈 نسبة الضغط: {((original_size - final_size) / original_size * 100):.1f}%")
        print(f"\n💡 نصيحة: افتح الملف للتأكد من الجودة العالية!")
        
    else:
        print(f"\n❌ فشل في الضغط")
    
    input("\n⏎ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
