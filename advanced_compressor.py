#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ضاغط PDF متقدم - Advanced PDF Compressor
ضغط قوي لتقليل الحجم إلى 1 MB أو أقل
"""

import fitz  # PyMuPDF
import os
import sys
from PIL import Image
import io

def analyze_pdf(file_path):
    """تحليل مفصل لملف PDF"""
    print(f"🔍 تحليل ملف: {file_path}")
    print("=" * 50)
    
    doc = fitz.open(file_path)
    
    # معلومات أساسية
    page_count = len(doc)
    file_size = os.path.getsize(file_path)
    
    print(f"📄 عدد الصفحات: {page_count}")
    print(f"📊 الحجم الحالي: {format_size(file_size)}")
    
    # تحليل الصور
    total_images = 0
    total_image_size = 0
    image_details = []
    
    for page_num in range(page_count):
        page = doc[page_num]
        images = page.get_images()
        
        for img_index, img in enumerate(images):
            try:
                xref = img[0]
                base_image = doc.extract_image(xref)
                
                image_info = {
                    'page': page_num + 1,
                    'xref': xref,
                    'width': base_image['width'],
                    'height': base_image['height'],
                    'size': len(base_image['image']),
                    'ext': base_image['ext']
                }
                
                image_details.append(image_info)
                total_images += 1
                total_image_size += image_info['size']
                
            except Exception as e:
                print(f"⚠️ خطأ في تحليل صورة: {e}")
    
    print(f"🖼️ عدد الصور: {total_images}")
    print(f"📷 حجم الصور الإجمالي: {format_size(total_image_size)}")
    print(f"📝 حجم النص والبيانات: {format_size(file_size - total_image_size)}")
    
    # عرض تفاصيل الصور الكبيرة
    large_images = [img for img in image_details if img['size'] > 50000]  # أكبر من 50KB
    if large_images:
        print(f"\n🔍 الصور الكبيرة (أكبر من 50KB):")
        for img in large_images[:5]:  # أول 5 صور
            print(f"   📷 صفحة {img['page']}: {img['width']}x{img['height']} - {format_size(img['size'])}")
    
    doc.close()
    return {
        'page_count': page_count,
        'file_size': file_size,
        'total_images': total_images,
        'total_image_size': total_image_size,
        'image_details': image_details
    }

def aggressive_compress_pdf(input_file, output_file, target_size_mb=1):
    """ضغط قوي لملف PDF"""
    print(f"\n🚀 بدء الضغط القوي...")
    print(f"🎯 الهدف: تقليل الحجم إلى {target_size_mb} MB أو أقل")
    
    try:
        doc = fitz.open(input_file)
        original_size = os.path.getsize(input_file)
        target_size = target_size_mb * 1024 * 1024  # تحويل إلى بايت
        
        print(f"📊 الحجم الأصلي: {format_size(original_size)}")
        print(f"🎯 الحجم المستهدف: {format_size(target_size)}")
        
        # مستوى ضغط قوي
        compression_settings = {
            'image_quality': 30,  # جودة منخفضة جداً
            'max_width': 600,     # عرض أقصى للصور
            'max_height': 800,    # ارتفاع أقصى للصور
            'dpi': 72            # دقة منخفضة
        }
        
        total_pages = len(doc)
        processed_images = 0
        
        for page_num in range(total_pages):
            page = doc[page_num]
            images = page.get_images()
            
            print(f"🔄 معالجة صفحة {page_num + 1}/{total_pages}...")
            
            for img_index, img in enumerate(images):
                try:
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    
                    # ضغط الصورة بقوة
                    compressed_image = super_compress_image(
                        image_bytes, 
                        compression_settings
                    )
                    
                    # استبدال الصورة
                    if compressed_image and len(compressed_image) < len(image_bytes):
                        # إنشاء كائن صورة جديد
                        img_dict = {
                            "type": "image",
                            "bbox": fitz.Rect(0, 0, 100, 100),  # مربع افتراضي
                            "stream": compressed_image
                        }
                        processed_images += 1
                    
                except Exception as e:
                    print(f"   ⚠️ خطأ في ضغط صورة: {e}")
                    continue
        
        print(f"✅ تم معالجة {processed_images} صورة")
        
        # حفظ مع أقصى ضغط
        print(f"💾 حفظ الملف المضغوط...")
        doc.save(
            output_file,
            garbage=4,      # إزالة البيانات غير المستخدمة
            deflate=True,   # ضغط إضافي
            clean=True,     # تنظيف الملف
            ascii=False,    # تحسين الحجم
            expand=0,       # عدم توسيع المحتوى
            linear=False,   # تحسين للحجم وليس للسرعة
            pretty=False    # عدم تنسيق جميل (يوفر مساحة)
        )
        
        doc.close()
        
        # التحقق من النتيجة
        if os.path.exists(output_file):
            compressed_size = os.path.getsize(output_file)
            compression_ratio = (1 - compressed_size / original_size) * 100
            
            print(f"\n🎉 تم الضغط بنجاح!")
            print(f"📉 الحجم الجديد: {format_size(compressed_size)}")
            print(f"💾 نسبة الضغط: {compression_ratio:.1f}%")
            
            if compressed_size <= target_size:
                print(f"✅ تم تحقيق الهدف! الحجم أقل من {target_size_mb} MB")
            else:
                print(f"⚠️ الحجم أكبر من المستهدف بـ {format_size(compressed_size - target_size)}")
                
                # محاولة ضغط إضافي
                if compressed_size > target_size:
                    print(f"🔄 محاولة ضغط إضافي...")
                    ultra_compress_pdf(output_file, target_size_mb)
            
            return True
        else:
            print(f"❌ فشل في إنشاء الملف المضغوط")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الضغط: {e}")
        return False

def super_compress_image(image_bytes, settings):
    """ضغط صورة بأقصى قوة"""
    try:
        # فتح الصورة
        image = Image.open(io.BytesIO(image_bytes))
        
        # تحويل إلى RGB
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')
        
        # تقليل الحجم بقوة
        max_width = settings['max_width']
        max_height = settings['max_height']
        
        # حساب النسبة الجديدة
        ratio = min(max_width / image.width, max_height / image.height)
        if ratio < 1:
            new_width = int(image.width * ratio)
            new_height = int(image.height * ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # ضغط بجودة منخفضة جداً
        output = io.BytesIO()
        image.save(
            output, 
            format='JPEG',
            quality=settings['image_quality'],
            optimize=True,
            progressive=True  # ضغط تدريجي
        )
        
        return output.getvalue()
        
    except Exception as e:
        print(f"خطأ في ضغط الصورة: {e}")
        return None

def ultra_compress_pdf(file_path, target_mb):
    """ضغط فائق للوصول للحجم المستهدف"""
    print(f"⚡ تطبيق ضغط فائق...")
    
    try:
        doc = fitz.open(file_path)
        
        # إعدادات ضغط فائق
        ultra_settings = {
            'image_quality': 15,  # جودة منخفضة جداً
            'max_width': 400,     # عرض أصغر
            'max_height': 600,    # ارتفاع أصغر
            'dpi': 50            # دقة منخفضة جداً
        }
        
        # معالجة جميع الصور مرة أخرى
        for page_num in range(len(doc)):
            page = doc[page_num]
            images = page.get_images()
            
            for img_index, img in enumerate(images):
                try:
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    
                    # ضغط فائق
                    ultra_compressed = super_compress_image(image_bytes, ultra_settings)
                    
                    if ultra_compressed:
                        # محاولة استبدال الصورة
                        pass
                        
                except Exception as e:
                    continue
        
        # حفظ مع ضغط أقصى
        temp_file = file_path.replace('.pdf', '_ultra_temp.pdf')
        doc.save(
            temp_file,
            garbage=4,
            deflate=True,
            clean=True,
            ascii=False,
            expand=0,
            linear=False,
            pretty=False
        )
        
        doc.close()
        
        # استبدال الملف الأصلي
        if os.path.exists(temp_file):
            os.replace(temp_file, file_path)
            
            new_size = os.path.getsize(file_path)
            print(f"⚡ الحجم بعد الضغط الفائق: {format_size(new_size)}")
            
            return new_size <= (target_mb * 1024 * 1024)
        
    except Exception as e:
        print(f"خطأ في الضغط الفائق: {e}")
        return False

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def main():
    """الدالة الرئيسية"""
    input_file = "file.pdf"
    output_file = "file_compressed_1MB.pdf"
    target_size = 1  # 1 MB
    
    print("🎯 ضاغط PDF المتقدم - هدف 1 MB أو أقل")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"❌ الملف {input_file} غير موجود!")
        return
    
    # تحليل الملف
    analysis = analyze_pdf(input_file)
    
    # الضغط
    success = aggressive_compress_pdf(input_file, output_file, target_size)
    
    if success:
        print(f"\n🎊 تم إنشاء الملف المضغوط: {output_file}")
    else:
        print(f"\n❌ فشل في الضغط")
    
    input("\n⏎ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
