#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Compressor Installation Script
سكربت تثبيت ضاغط ملفات PDF
"""

import subprocess
import sys
import os

def install_requirements():
    """تثبيت المتطلبات"""
    print("🚀 بدء تثبيت متطلبات ضاغط ملفات PDF...")
    print("🚀 Starting PDF Compressor requirements installation...")
    
    try:
        # تحديث pip
        print("\n📦 تحديث pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # تثبيت المتطلبات
        print("\n📚 تثبيت المكتبات المطلوبة...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("\n✅ تم التثبيت بنجاح!")
        print("✅ Installation completed successfully!")
        
        # اختبار الاستيراد
        print("\n🧪 اختبار المكتبات...")
        try:
            import fitz
            import tkinterdnd2
            from PIL import Image
            print("✅ جميع المكتبات تعمل بشكل صحيح!")
            print("✅ All libraries are working correctly!")
        except ImportError as e:
            print(f"⚠️ تحذير: مشكلة في استيراد المكتبة: {e}")
            print(f"⚠️ Warning: Library import issue: {e}")
        
        print("\n🎉 يمكنك الآن تشغيل البرنامج باستخدام:")
        print("🎉 You can now run the program using:")
        print("python pdf_compressor.py")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ خطأ في التثبيت: {e}")
        print(f"❌ Installation error: {e}")
        print("\n💡 جرب تشغيل الأمر التالي يدوياً:")
        print("💡 Try running this command manually:")
        print("pip install -r requirements.txt")
        
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        print(f"❌ Unexpected error: {e}")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("  ضاغط ملفات PDF - PDF Compressor")
    print("  سكربت التثبيت - Installation Script")
    print("=" * 50)
    
    # التحقق من وجود ملف المتطلبات
    if not os.path.exists("requirements.txt"):
        print("❌ ملف requirements.txt غير موجود!")
        print("❌ requirements.txt file not found!")
        return
    
    # بدء التثبيت
    install_requirements()
    
    input("\n⏎ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
