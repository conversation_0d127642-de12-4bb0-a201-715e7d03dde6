#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار ضاغط PDF مع ملف حقيقي
Test PDF Compressor with real file
"""

import os
import sys
import time
import fitz  # PyMuPDF
from pathlib import Path

def test_pdf_compression():
    """اختبار ضغط ملف PDF"""
    
    print("🧪 اختبار ضاغط ملفات PDF مع ملف حقيقي")
    print("🧪 Testing PDF Compressor with real file")
    print("=" * 60)
    
    # التحقق من وجود الملف
    test_file = "file.pdf"
    if not os.path.exists(test_file):
        print(f"❌ الملف {test_file} غير موجود!")
        return False
    
    # عرض معلومات الملف الأصلي
    original_size = os.path.getsize(test_file)
    print(f"\n📄 الملف الأصلي: {test_file}")
    print(f"📊 الحجم الأصلي: {format_size(original_size)}")
    
    try:
        # فتح الملف وعرض معلوماته
        doc = fitz.open(test_file)
        page_count = len(doc)
        print(f"📑 عدد الصفحات: {page_count}")
        
        # عد الصور في الملف
        total_images = 0
        for page_num in range(page_count):
            page = doc[page_num]
            images = page.get_images()
            total_images += len(images)
        
        print(f"🖼️ عدد الصور: {total_images}")
        doc.close()
        
        # اختبار الضغط بمستويات مختلفة
        quality_levels = {
            "مرتفع": {"quality": 95, "dpi": 150},
            "متوسط": {"quality": 75, "dpi": 100},
            "منخفض": {"quality": 50, "dpi": 72}
        }
        
        print(f"\n🔄 بدء اختبار الضغط...")
        
        for level_name, settings in quality_levels.items():
            print(f"\n⚙️ اختبار مستوى: {level_name}")
            print(f"   📊 الجودة: {settings['quality']}%")
            
            # إنشاء اسم الملف المضغوط
            output_file = test_file.replace('.pdf', f'_compressed_{level_name}.pdf')
            
            # ضغط الملف
            success = compress_pdf_file(test_file, output_file, settings)
            
            if success and os.path.exists(output_file):
                compressed_size = os.path.getsize(output_file)
                compression_ratio = (1 - compressed_size / original_size) * 100
                
                print(f"   ✅ تم الضغط بنجاح!")
                print(f"   📉 الحجم الجديد: {format_size(compressed_size)}")
                print(f"   💾 توفير: {compression_ratio:.1f}%")
                print(f"   📁 الملف المحفوظ: {output_file}")
            else:
                print(f"   ❌ فشل في الضغط!")
        
        print(f"\n🎉 انتهى الاختبار!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def compress_pdf_file(input_file, output_file, quality_settings):
    """ضغط ملف PDF"""
    try:
        print(f"   🔄 جاري الضغط...")
        
        # فتح المستند
        doc = fitz.open(input_file)
        
        # ضغط الصور في كل صفحة
        total_pages = len(doc)
        for page_num in range(total_pages):
            page = doc[page_num]
            
            # الحصول على قائمة الصور
            image_list = page.get_images()
            
            for img_index, img in enumerate(image_list):
                try:
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_bytes = base_image["image"]
                    
                    # ضغط الصورة
                    compressed_image = compress_image(image_bytes, quality_settings)
                    
                    # استبدال الصورة (محاولة بسيطة)
                    # ملاحظة: هذا مبسط وقد لا يعمل مع جميع أنواع PDF
                    
                except Exception as img_error:
                    print(f"   ⚠️ تحذير: فشل في ضغط صورة: {img_error}")
                    continue
        
        # حفظ المستند المضغوط
        doc.save(output_file, garbage=4, deflate=True, clean=True)
        doc.close()
        
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في الضغط: {e}")
        return False

def compress_image(image_bytes, quality_settings):
    """ضغط صورة باستخدام Pillow"""
    try:
        from PIL import Image
        import io
        
        # تحويل البايتات إلى صورة
        image = Image.open(io.BytesIO(image_bytes))
        
        # تحويل إلى RGB إذا لزم الأمر
        if image.mode in ('RGBA', 'LA', 'P'):
            image = image.convert('RGB')
        
        # تقليل الدقة إذا كانت عالية جداً
        max_dimension = 1200 if quality_settings["quality"] > 70 else 800
        if max(image.size) > max_dimension:
            image.thumbnail((max_dimension, max_dimension), Image.Resampling.LANCZOS)
        
        # ضغط الصورة
        output = io.BytesIO()
        image.save(output, format='JPEG', 
                  quality=quality_settings["quality"], 
                  optimize=True)
        
        return output.getvalue()
        
    except Exception as e:
        print(f"   ⚠️ خطأ في ضغط الصورة: {e}")
        return image_bytes

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def cleanup_test_files():
    """تنظيف ملفات الاختبار"""
    print(f"\n🧹 تنظيف ملفات الاختبار...")
    
    test_patterns = [
        "*_compressed_*.pdf",
        "*_compressed_temp.pdf"
    ]
    
    import glob
    cleaned_count = 0
    
    for pattern in test_patterns:
        files = glob.glob(pattern)
        for file in files:
            try:
                os.remove(file)
                print(f"   🗑️ تم حذف: {file}")
                cleaned_count += 1
            except Exception as e:
                print(f"   ⚠️ فشل في حذف {file}: {e}")
    
    if cleaned_count > 0:
        print(f"   ✅ تم تنظيف {cleaned_count} ملف")
    else:
        print(f"   ℹ️ لا توجد ملفات للتنظيف")

def main():
    """الدالة الرئيسية"""
    print("🔬 مختبر ضاغط ملفات PDF - اختبار مع ملف حقيقي")
    print("🔬 PDF Compressor Lab - Testing with real file")
    print("=" * 60)
    
    # تشغيل الاختبار
    success = test_pdf_compression()
    
    # سؤال المستخدم عن التنظيف
    if success:
        print(f"\n" + "=" * 60)
        choice = input("❓ هل تريد حذف الملفات المضغوطة للاختبار؟ (y/n): ").lower()
        if choice in ['y', 'yes', 'نعم']:
            cleanup_test_files()
        else:
            print("   ℹ️ تم الاحتفاظ بالملفات المضغوطة")
    
    print(f"\n🎊 انتهى الاختبار!")
    input("⏎ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
