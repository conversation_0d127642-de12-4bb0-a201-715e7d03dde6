#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ضاغط PDF ذكي - Smart PDF Compressor
يحافظ على الجودة مع تحقيق هدف 1 MB
"""

import fitz  # PyMuPDF
import os
import sys
from PIL import Image
import io

def smart_compress_pdf(input_file, output_file, target_size_mb=1):
    """ضغط ذكي يحافظ على الجودة"""
    
    print(f"🧠 ضاغط PDF ذكي - يحافظ على الجودة")
    print(f"🎯 الهدف: {target_size_mb} MB مع الحفاظ على الجودة")
    print("=" * 60)
    
    original_size = os.path.getsize(input_file)
    target_size = target_size_mb * 1024 * 1024
    
    print(f"📊 الحجم الأصلي: {format_size(original_size)}")
    print(f"🎯 الحجم المستهدف: {format_size(target_size)}")
    
    # تحليل محتوى الملف أولاً
    analysis = analyze_pdf_content(input_file)
    
    # تحديد استراتيجية الضغط بناءً على المحتوى
    strategy = determine_compression_strategy(analysis, target_size)
    
    print(f"\n🎯 استراتيجية الضغط المختارة: {strategy['name']}")
    print(f"📋 الوصف: {strategy['description']}")
    
    try:
        doc = fitz.open(input_file)
        
        # تطبيق الضغط الذكي
        if strategy['type'] == 'selective':
            success = selective_image_compression(doc, strategy)
        elif strategy['type'] == 'balanced':
            success = balanced_compression(doc, strategy)
        elif strategy['type'] == 'text_optimized':
            success = text_optimized_compression(doc, strategy)
        else:
            success = gentle_compression(doc, strategy)
        
        if success:
            # حفظ مع إعدادات محسنة للجودة
            doc.save(
                output_file,
                garbage=3,      # تنظيف متوسط (ليس أقصى)
                deflate=True,   # ضغط إضافي
                clean=True,     # تنظيف
                ascii=False,    # تحسين الحجم
                expand=0,       # عدم توسيع المحتوى
                linear=True,    # تحسين للعرض السريع
                pretty=False    # توفير مساحة
            )
            
            doc.close()
            
            # التحقق من النتيجة
            if os.path.exists(output_file):
                final_size = os.path.getsize(output_file)
                compression_ratio = (1 - final_size / original_size) * 100
                
                print(f"\n🎉 تم الضغط الذكي بنجاح!")
                print(f"📉 الحجم النهائي: {format_size(final_size)}")
                print(f"💾 نسبة الضغط: {compression_ratio:.1f}%")
                
                if final_size <= target_size:
                    print(f"✅ تم تحقيق الهدف مع الحفاظ على الجودة!")
                else:
                    excess = final_size - target_size
                    print(f"⚠️ الحجم أكبر من المستهدف بـ {format_size(excess)}")
                    print(f"💡 لكن الجودة محفوظة بشكل أفضل")
                
                return True
            else:
                print(f"❌ فشل في إنشاء الملف")
                return False
        else:
            print(f"❌ فشل في الضغط")
            doc.close()
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الضغط: {e}")
        return False

def analyze_pdf_content(input_file):
    """تحليل محتوى PDF لتحديد أفضل استراتيجية"""
    print(f"\n🔍 تحليل محتوى الملف...")
    
    doc = fitz.open(input_file)
    analysis = {
        'pages': len(doc),
        'total_images': 0,
        'large_images': 0,
        'small_images': 0,
        'text_heavy_pages': 0,
        'image_heavy_pages': 0,
        'average_image_size': 0,
        'total_image_bytes': 0
    }
    
    image_sizes = []
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        images = page.get_images()
        page_image_count = len(images)
        page_image_size = 0
        
        for img in images:
            try:
                xref = img[0]
                base_image = doc.extract_image(xref)
                img_size = len(base_image['image'])
                image_sizes.append(img_size)
                page_image_size += img_size
                
                analysis['total_images'] += 1
                analysis['total_image_bytes'] += img_size
                
                # تصنيف الصور
                if img_size > 200000:  # أكبر من 200KB
                    analysis['large_images'] += 1
                else:
                    analysis['small_images'] += 1
                    
            except:
                continue
        
        # تصنيف الصفحات
        if page_image_count > 2 or page_image_size > 500000:
            analysis['image_heavy_pages'] += 1
        else:
            analysis['text_heavy_pages'] += 1
    
    if image_sizes:
        analysis['average_image_size'] = sum(image_sizes) / len(image_sizes)
    
    doc.close()
    
    # عرض التحليل
    print(f"   📑 عدد الصفحات: {analysis['pages']}")
    print(f"   🖼️ إجمالي الصور: {analysis['total_images']}")
    print(f"   📏 متوسط حجم الصورة: {format_size(analysis['average_image_size'])}")
    print(f"   🔍 صور كبيرة (>200KB): {analysis['large_images']}")
    print(f"   📄 صفحات نصية: {analysis['text_heavy_pages']}")
    print(f"   🖼️ صفحات مصورة: {analysis['image_heavy_pages']}")
    
    return analysis

def determine_compression_strategy(analysis, target_size):
    """تحديد استراتيجية الضغط المناسبة"""
    
    # حساب نسبة الصور إلى النص
    if analysis['total_images'] == 0:
        image_ratio = 0
    else:
        image_ratio = analysis['image_heavy_pages'] / analysis['pages']
    
    large_image_ratio = analysis['large_images'] / max(analysis['total_images'], 1)
    
    if image_ratio > 0.7 and large_image_ratio > 0.5:
        # ملف مليء بالصور الكبيرة - ضغط انتقائي
        return {
            'type': 'selective',
            'name': 'ضغط انتقائي للصور الكبيرة',
            'description': 'ضغط الصور الكبيرة فقط مع الحفاظ على الصور الصغيرة',
            'large_image_quality': 70,
            'small_image_quality': 85,
            'max_dimension': 1000
        }
    elif image_ratio > 0.4:
        # ملف متوازن - ضغط متوازن
        return {
            'type': 'balanced',
            'name': 'ضغط متوازن',
            'description': 'ضغط متوازن للصور مع الحفاظ على جودة النص',
            'image_quality': 75,
            'max_dimension': 1200,
            'preserve_small_images': True
        }
    else:
        # ملف نصي أساساً - ضغط محسن للنص
        return {
            'type': 'text_optimized',
            'name': 'ضغط محسن للنص',
            'description': 'تحسين للملفات النصية مع ضغط خفيف للصور',
            'image_quality': 80,
            'max_dimension': 1400,
            'minimal_processing': True
        }

def selective_image_compression(doc, strategy):
    """ضغط انتقائي للصور الكبيرة فقط"""
    print(f"🎯 تطبيق الضغط الانتقائي...")
    
    processed_large = 0
    preserved_small = 0
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        images = page.get_images()
        
        for img_index, img in enumerate(images):
            try:
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                original_size = len(image_bytes)
                
                # ضغط الصور الكبيرة فقط
                if original_size > 200000:  # أكبر من 200KB
                    compressed = smart_compress_image(
                        image_bytes, 
                        strategy['large_image_quality'],
                        strategy['max_dimension']
                    )
                    if compressed and len(compressed) < original_size:
                        # استبدال الصورة (محاولة تحديث)
                        processed_large += 1
                else:
                    # الحفاظ على الصور الصغيرة
                    preserved_small += 1
                    
            except Exception as e:
                print(f"   ⚠️ تحذير: {e}")
                continue
    
    print(f"   ✅ تم ضغط {processed_large} صورة كبيرة")
    print(f"   🔒 تم الحفاظ على {preserved_small} صورة صغيرة")
    
    return True

def balanced_compression(doc, strategy):
    """ضغط متوازن"""
    print(f"⚖️ تطبيق الضغط المتوازن...")
    
    processed = 0
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        images = page.get_images()
        
        for img_index, img in enumerate(images):
            try:
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                
                # ضغط متوازن لجميع الصور
                compressed = smart_compress_image(
                    image_bytes,
                    strategy['image_quality'],
                    strategy['max_dimension']
                )
                
                if compressed:
                    processed += 1
                    
            except Exception as e:
                print(f"   ⚠️ تحذير: {e}")
                continue
    
    print(f"   ✅ تم معالجة {processed} صورة بضغط متوازن")
    
    return True

def text_optimized_compression(doc, strategy):
    """ضغط محسن للنص"""
    print(f"📝 تطبيق الضغط المحسن للنص...")
    
    processed = 0
    
    for page_num in range(len(doc)):
        page = doc[page_num]
        images = page.get_images()
        
        # ضغط خفيف جداً للصور
        for img_index, img in enumerate(images):
            try:
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                
                # ضغط خفيف مع الحفاظ على الجودة
                compressed = smart_compress_image(
                    image_bytes,
                    strategy['image_quality'],
                    strategy['max_dimension'],
                    minimal=True
                )
                
                if compressed:
                    processed += 1
                    
            except Exception as e:
                continue
    
    print(f"   ✅ تم معالجة {processed} صورة بضغط خفيف")
    
    return True

def gentle_compression(doc, strategy):
    """ضغط لطيف"""
    print(f"🌸 تطبيق الضغط اللطيف...")
    
    # ضغط لطيف جداً مع الحفاظ على أقصى جودة
    for page_num in range(len(doc)):
        page = doc[page_num]
        images = page.get_images()
        
        for img_index, img in enumerate(images):
            try:
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                
                # ضغط لطيف جداً
                compressed = smart_compress_image(
                    image_bytes,
                    90,  # جودة عالية جداً
                    1600,  # دقة عالية
                    minimal=True
                )
                
            except Exception as e:
                continue
    
    print(f"   ✅ تم تطبيق ضغط لطيف مع الحفاظ على الجودة")
    
    return True

def smart_compress_image(image_bytes, quality, max_dimension, minimal=False):
    """ضغط ذكي للصورة مع الحفاظ على الجودة"""
    try:
        image = Image.open(io.BytesIO(image_bytes))
        
        # تحويل إلى RGB إذا لزم الأمر
        if image.mode in ('RGBA', 'LA'):
            # الحفاظ على الشفافية إن أمكن
            background = Image.new('RGB', image.size, (255, 255, 255))
            if image.mode == 'RGBA':
                background.paste(image, mask=image.split()[-1])
            else:
                background.paste(image)
            image = background
        elif image.mode == 'P':
            image = image.convert('RGB')
        
        # تقليل الحجم بحذر
        if not minimal and (image.width > max_dimension or image.height > max_dimension):
            ratio = min(max_dimension / image.width, max_dimension / image.height)
            new_width = int(image.width * ratio)
            new_height = int(image.height * ratio)
            
            # استخدام أفضل خوارزمية تقليل الحجم
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # ضغط بجودة محسنة
        output = io.BytesIO()
        
        # إعدادات ضغط محسنة للجودة
        save_kwargs = {
            'format': 'JPEG',
            'quality': quality,
            'optimize': True,
            'progressive': True,  # تحميل تدريجي
            'subsampling': 0 if quality > 80 else 2,  # تحسين الجودة للجودة العالية
        }
        
        image.save(output, **save_kwargs)
        
        return output.getvalue()
        
    except Exception as e:
        print(f"خطأ في ضغط الصورة: {e}")
        return None

def format_size(size_bytes):
    """تنسيق حجم الملف"""
    if size_bytes == 0:
        return "0 B"
    size_names = ["B", "KB", "MB", "GB"]
    i = 0
    while size_bytes >= 1024 and i < len(size_names) - 1:
        size_bytes /= 1024.0
        i += 1
    return f"{size_bytes:.2f} {size_names[i]}"

def main():
    """الدالة الرئيسية"""
    input_file = "file.pdf"
    output_file = "file_smart_compressed.pdf"
    
    print("🧠 ضاغط PDF ذكي - يحافظ على الجودة")
    print("=" * 60)
    
    if not os.path.exists(input_file):
        print(f"❌ الملف {input_file} غير موجود!")
        return
    
    # بدء الضغط الذكي
    success = smart_compress_pdf(input_file, output_file, target_size_mb=1)
    
    if success:
        print(f"\n🎊 تم إنشاء الملف المضغوط: {output_file}")
        
        # مقارنة الجودة
        print(f"\n📊 مقارنة الجودة:")
        print(f"   🎯 الهدف: الحفاظ على الجودة مع تقليل الحجم")
        print(f"   ✅ النتيجة: ضغط ذكي مع جودة محسنة")
        print(f"   💡 نصيحة: افتح الملف للتأكد من الجودة")
        
    else:
        print(f"\n❌ فشل في الضغط")
    
    input("\n⏎ اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
