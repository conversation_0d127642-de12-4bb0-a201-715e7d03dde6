#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Compressor Test Script
سكربت اختبار ضاغط ملفات PDF
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock

# إضافة المجلد الحالي للمسار
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class TestPDFCompressor(unittest.TestCase):
    """اختبارات ضاغط ملفات PDF"""
    
    def setUp(self):
        """إعداد الاختبارات"""
        self.test_pdf_path = "file.pdf"
        
    def test_imports(self):
        """اختبار استيراد المكتبات"""
        try:
            import fitz
            import tkinterdnd2
            from PIL import Image
            import config
            self.assertTrue(True, "جميع المكتبات متوفرة")
        except ImportError as e:
            self.fail(f"فشل في استيراد المكتبة: {e}")
    
    def test_config_loading(self):
        """اختبار تحميل الإعدادات"""
        try:
            from config import GUI_CONFIG, QUALITY_SETTINGS, get_message
            
            # اختبار إعدادات الواجهة
            self.assertIn("window_title", GUI_CONFIG)
            self.assertIn("window_size", GUI_CONFIG)
            
            # اختبار إعدادات الجودة
            self.assertIn("مرتفع", QUALITY_SETTINGS)
            self.assertIn("متوسط", QUALITY_SETTINGS)
            self.assertIn("منخفض", QUALITY_SETTINGS)
            
            # اختبار الرسائل
            message = get_message("ready_status", "ar")
            self.assertIsInstance(message, str)
            
            print("✅ تم تحميل الإعدادات بنجاح")
            
        except Exception as e:
            self.fail(f"فشل في تحميل الإعدادات: {e}")
    
    def test_pdf_file_exists(self):
        """اختبار وجود ملف PDF للاختبار"""
        if os.path.exists(self.test_pdf_path):
            file_size = os.path.getsize(self.test_pdf_path)
            self.assertGreater(file_size, 0, "ملف PDF فارغ")
            print(f"✅ ملف PDF موجود - الحجم: {file_size} بايت")
        else:
            print("⚠️ ملف PDF للاختبار غير موجود")
    
    def test_format_size_function(self):
        """اختبار دالة تنسيق الحجم"""
        # محاكاة الدالة
        def format_size(size_bytes):
            if size_bytes == 0:
                return "0 B"
            size_names = ["B", "KB", "MB", "GB"]
            i = 0
            while size_bytes >= 1024 and i < len(size_names) - 1:
                size_bytes /= 1024.0
                i += 1
            return f"{size_bytes:.2f} {size_names[i]}"
        
        # اختبارات مختلفة
        self.assertEqual(format_size(0), "0 B")
        self.assertEqual(format_size(512), "512.00 B")
        self.assertEqual(format_size(1024), "1.00 KB")
        self.assertEqual(format_size(1048576), "1.00 MB")
        
        print("✅ دالة تنسيق الحجم تعمل بشكل صحيح")
    
    @patch('tkinter.Tk')
    def test_gui_initialization(self, mock_tk):
        """اختبار تهيئة الواجهة الرسومية"""
        try:
            # محاكاة tkinter
            mock_root = MagicMock()
            mock_tk.return_value = mock_root
            
            # محاولة استيراد الكلاس
            from pdf_compressor import PDFCompressor
            
            # إنشاء مثيل (مع المحاكاة)
            with patch('tkinterdnd2.Tk', return_value=mock_root):
                app = PDFCompressor(mock_root)
                self.assertIsNotNone(app)
                
            print("✅ تهيئة الواجهة الرسومية تعمل")
            
        except Exception as e:
            print(f"⚠️ تحذير في اختبار الواجهة: {e}")

def run_basic_tests():
    """تشغيل اختبارات أساسية بدون unittest"""
    print("🧪 بدء الاختبارات الأساسية...")
    print("🧪 Starting basic tests...")
    print("=" * 50)
    
    # اختبار 1: المكتبات
    print("\n1️⃣ اختبار المكتبات...")
    try:
        import fitz
        print("   ✅ PyMuPDF متوفر")
    except ImportError:
        print("   ❌ PyMuPDF غير متوفر")
    
    try:
        import tkinterdnd2
        print("   ✅ tkinterdnd2 متوفر")
    except ImportError:
        print("   ❌ tkinterdnd2 غير متوفر")
    
    try:
        from PIL import Image
        print("   ✅ Pillow متوفر")
    except ImportError:
        print("   ❌ Pillow غير متوفر")
    
    # اختبار 2: الإعدادات
    print("\n2️⃣ اختبار الإعدادات...")
    try:
        from config import QUALITY_SETTINGS, get_message
        print("   ✅ ملف الإعدادات يعمل")
        print(f"   📊 مستويات الجودة: {list(QUALITY_SETTINGS.keys())}")
    except Exception as e:
        print(f"   ❌ خطأ في الإعدادات: {e}")
    
    # اختبار 3: ملف PDF
    print("\n3️⃣ اختبار ملف PDF...")
    test_file = "file.pdf"
    if os.path.exists(test_file):
        size = os.path.getsize(test_file)
        print(f"   ✅ ملف PDF موجود - الحجم: {size:,} بايت")
    else:
        print("   ⚠️ ملف PDF للاختبار غير موجود")
    
    # اختبار 4: السكربت الرئيسي
    print("\n4️⃣ اختبار السكربت الرئيسي...")
    try:
        import pdf_compressor
        print("   ✅ السكربت الرئيسي يمكن استيراده")
    except Exception as e:
        print(f"   ❌ خطأ في السكربت الرئيسي: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 انتهت الاختبارات الأساسية!")
    print("🎉 Basic tests completed!")

def main():
    """الدالة الرئيسية"""
    print("🔬 مختبر ضاغط ملفات PDF")
    print("🔬 PDF Compressor Test Suite")
    print("=" * 50)
    
    # تشغيل الاختبارات الأساسية
    run_basic_tests()
    
    # تشغيل اختبارات unittest إذا أردت
    choice = input("\n❓ هل تريد تشغيل اختبارات متقدمة؟ (y/n): ").lower()
    if choice in ['y', 'yes', 'نعم']:
        print("\n🚀 تشغيل الاختبارات المتقدمة...")
        unittest.main(argv=[''], exit=False, verbosity=2)

if __name__ == "__main__":
    main()
