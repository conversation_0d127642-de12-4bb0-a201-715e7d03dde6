#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF Compressor Configuration
إعدادات ضاغط ملفات PDF
"""

# إعدادات الواجهة الرسومية | GUI Settings
GUI_CONFIG = {
    "window_title": "ضاغط ملفات PDF - PDF Compressor",
    "window_size": "600x500",
    "background_color": "#f0f0f0",
    "font_family": "Arial",
    "title_font_size": 16,
    "normal_font_size": 10,
}

# إعدادات الجودة | Quality Settings
QUALITY_SETTINGS = {
    "مرتفع": {
        "quality": 95,
        "dpi": 150,
        "max_dimension": 1200,
        "description": "للمستندات المهمة"
    },
    "متوسط": {
        "quality": 75,
        "dpi": 100,
        "max_dimension": 1000,
        "description": "للاستخدام العام"
    },
    "منخفض": {
        "quality": 50,
        "dpi": 72,
        "max_dimension": 800,
        "description": "لتوفير أقصى مساحة"
    }
}

# إعدادات الضغط | Compression Settings
COMPRESSION_CONFIG = {
    "temp_suffix": "_compressed_temp",
    "output_suffix": "_compressed",
    "supported_formats": [".pdf"],
    "max_file_size_mb": 100,  # الحد الأقصى لحجم الملف بالميجابايت
}

# رسائل الواجهة | Interface Messages
MESSAGES = {
    "ar": {
        "drag_drop_text": "اسحب ملف PDF هنا أو انقر لاختيار ملف",
        "no_file_selected": "لم يتم اختيار ملف",
        "file_info": "معلومات الملف",
        "compression_settings": "إعدادات الضغط",
        "quality_level": "مستوى الجودة:",
        "compress_button": "ضغط الملف",
        "save_as_button": "حفظ باسم",
        "clear_button": "مسح",
        "ready_status": "جاهز للاستخدام",
        "compressing_status": "جاري الضغط...",
        "compression_failed": "فشل في الضغط",
        "file_loaded": "ملف محمل وجاهز للضغط",
        "compression_success": "تم الضغط بنجاح! توفير: {ratio:.1f}% - الحجم الجديد: {size}",
        "error_title": "خطأ",
        "success_title": "نجح",
        "pdf_only_error": "يرجى اختيار ملف PDF فقط",
        "load_file_error": "فشل في تحميل الملف: {error}",
        "compress_error": "فشل في ضغط الملف: {error}",
        "save_error": "فشل في حفظ الملف: {error}",
        "no_compressed_file": "لا يوجد ملف مضغوط للحفظ",
        "file_saved": "تم حفظ الملف في:\n{path}",
        "select_pdf_title": "اختر ملف PDF",
        "save_compressed_title": "حفظ الملف المضغوط"
    },
    "en": {
        "drag_drop_text": "Drag PDF file here or click to select",
        "no_file_selected": "No file selected",
        "file_info": "File Information",
        "compression_settings": "Compression Settings",
        "quality_level": "Quality Level:",
        "compress_button": "Compress File",
        "save_as_button": "Save As",
        "clear_button": "Clear",
        "ready_status": "Ready to use",
        "compressing_status": "Compressing...",
        "compression_failed": "Compression failed",
        "file_loaded": "File loaded and ready to compress",
        "compression_success": "Compression successful! Saved: {ratio:.1f}% - New size: {size}",
        "error_title": "Error",
        "success_title": "Success",
        "pdf_only_error": "Please select PDF files only",
        "load_file_error": "Failed to load file: {error}",
        "compress_error": "Failed to compress file: {error}",
        "save_error": "Failed to save file: {error}",
        "no_compressed_file": "No compressed file to save",
        "file_saved": "File saved to:\n{path}",
        "select_pdf_title": "Select PDF File",
        "save_compressed_title": "Save Compressed File"
    }
}

# إعدادات متقدمة | Advanced Settings
ADVANCED_CONFIG = {
    "enable_threading": True,
    "progress_update_interval": 0.1,  # ثواني
    "auto_save_compressed": False,
    "show_compression_details": True,
    "backup_original": False,
    "default_language": "ar",  # ar أو en
}

# ألوان الواجهة | Interface Colors
COLORS = {
    "primary": "#2196F3",
    "secondary": "#FFC107",
    "success": "#4CAF50",
    "error": "#F44336",
    "warning": "#FF9800",
    "background": "#f0f0f0",
    "drop_zone": "#e8e8e8",
    "text": "#333333",
}

def get_message(key, lang="ar", **kwargs):
    """الحصول على رسالة مترجمة"""
    try:
        message = MESSAGES[lang][key]
        if kwargs:
            return message.format(**kwargs)
        return message
    except KeyError:
        # العودة للإنجليزية إذا لم توجد الترجمة العربية
        try:
            message = MESSAGES["en"][key]
            if kwargs:
                return message.format(**kwargs)
            return message
        except KeyError:
            return key

def get_quality_config(quality_name):
    """الحصول على إعدادات الجودة"""
    return QUALITY_SETTINGS.get(quality_name, QUALITY_SETTINGS["متوسط"])
