# ضاغط ملفات PDF - PDF Compressor

## 📋 الوصف | Description

أداة قوية وسهلة الاستخدام لضغط ملفات PDF مع واجهة رسومية متقدمة تدعم السحب والإفلات.

A powerful and user-friendly PDF compression tool with an advanced GUI that supports drag-and-drop functionality.

## ✨ المميزات | Features

- 🖱️ **واجهة رسومية بسيطة وأنيقة** - Simple and elegant GUI
- 📁 **دعم السحب والإفلات** - Drag and drop support
- ⚙️ **ثلاثة مستويات للجودة** - Three quality levels (High, Medium, Low)
- 📊 **عرض أحجام الملفات** - Display file sizes before and after compression
- 💾 **حفظ مرن** - Flexible saving options
- 🔄 **شريط تقدم** - Progress bar
- ⚡ **معالجة متعددة الخيوط** - Multi-threaded processing
- 🛡️ **معالجة الأخطاء** - Error handling with user-friendly messages

## 🚀 التثبيت | Installation

### المتطلبات | Requirements
- Python 3.7 أو أحدث | Python 3.7 or newer
- نظام التشغيل: Windows, macOS, Linux

### خطوات التثبيت | Installation Steps

1. **استنساخ المشروع | Clone the project:**
```bash
git clone <repository-url>
cd pdf-compressor
```

2. **تثبيت المتطلبات | Install requirements:**
```bash
pip install -r requirements.txt
```

3. **تشغيل البرنامج | Run the application:**
```bash
python pdf_compressor.py
```

## 📖 طريقة الاستخدام | Usage

### الطريقة الأولى: السحب والإفلات | Method 1: Drag and Drop
1. اسحب ملف PDF إلى المنطقة المخصصة في الواجهة
2. اختر مستوى الجودة المطلوب
3. انقر على "ضغط الملف"
4. احفظ الملف المضغوط

### الطريقة الثانية: اختيار الملف | Method 2: File Selection
1. انقر على منطقة اختيار الملف
2. اختر ملف PDF من جهازك
3. اختر مستوى الجودة
4. انقر على "ضغط الملف"
5. احفظ النتيجة

## ⚙️ مستويات الجودة | Quality Levels

| المستوى | الجودة | الدقة | الاستخدام المناسب |
|---------|--------|-------|------------------|
| **مرتفع** | 95% | 150 DPI | للمستندات المهمة |
| **متوسط** | 75% | 100 DPI | للاستخدام العام |
| **منخفض** | 50% | 72 DPI | لتوفير أقصى مساحة |

| Level | Quality | DPI | Best For |
|-------|---------|-----|----------|
| **High** | 95% | 150 DPI | Important documents |
| **Medium** | 75% | 100 DPI | General use |
| **Low** | 50% | 72 DPI | Maximum space saving |

## 🔧 المكتبات المستخدمة | Libraries Used

- **PyMuPDF (fitz)**: معالجة ملفات PDF | PDF processing
- **tkinter**: الواجهة الرسومية | GUI framework
- **tkinterdnd2**: دعم السحب والإفلات | Drag and drop support
- **Pillow (PIL)**: معالجة الصور | Image processing
- **threading**: المعالجة المتوازية | Multi-threading

## 🐛 استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

1. **خطأ في تثبيت tkinterdnd2:**
```bash
pip install --upgrade tkinterdnd2
```

2. **خطأ في PyMuPDF:**
```bash
pip install --upgrade PyMuPDF
```

3. **مشاكل في الواجهة الرسومية على Linux:**
```bash
sudo apt-get install python3-tk
```

## 📝 ملاحظات | Notes

- يدعم البرنامج ملفات PDF فقط
- الضغط يعتمد على محتوى الملف (الصور تضغط أكثر من النصوص)
- النسخة المضغوطة تحفظ بجانب الملف الأصلي بإضافة "_compressed"

- The program supports PDF files only
- Compression depends on file content (images compress more than text)
- Compressed version is saved next to original file with "_compressed" suffix

## 🤝 المساهمة | Contributing

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إرسال Pull Request

Contributions are welcome! Please:
1. Fork the project
2. Create a feature branch
3. Submit a Pull Request

## 📄 الترخيص | License

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل

This project is licensed under the MIT License - see the LICENSE file for details.

## 📞 الدعم | Support

إذا واجهت أي مشاكل، يرجى إنشاء Issue في المستودع.

If you encounter any issues, please create an Issue in the repository.

---

**تم التطوير بواسطة | Developed by:** AI Assistant  
**الإصدار | Version:** 1.0.0  
**تاريخ التحديث | Last Updated:** 2024
